package com.tvplayer.webdav.ui.details

import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.tvplayer.webdav.R
import com.tvplayer.webdav.data.model.Actor
import com.tvplayer.webdav.data.model.MediaItem
import dagger.hilt.android.AndroidEntryPoint
import java.net.URLDecoder
import java.nio.charset.StandardCharsets

/**
 * 视频详情页面Fragment
 * 根据设计图实现视频详情页面布局
 */
@AndroidEntryPoint
class VideoDetailsFragment : Fragment() {

    private val viewModel: VideoDetailsViewModel by viewModels()
    
    private lateinit var mediaItem: MediaItem
    
    // UI组件
    private lateinit var ivBackdrop: ImageView
    private lateinit var tvTitle: TextView
    private lateinit var btnPlay: Button
    private lateinit var tvRating: TextView
    private lateinit var tvYear: TextView
    private lateinit var tvDuration: TextView
    private lateinit var tvGenre: TextView
    private lateinit var tvFileSize: TextView
    private lateinit var tvOverview: TextView
    private lateinit var movieInfoContainer: LinearLayout
    private lateinit var scrollView: ScrollView
    private lateinit var scrollOverlay: View

    // 滚动位置常量
    private val FIRST_SCREEN_HEIGHT = 600 // dp转换为px后使用
    private var firstScreenHeightPx = 0

    // 演员表组件
    private var rvActors: RecyclerView? = null
    private var tvFilename: TextView? = null
    private var tvSourcePath: TextView? = null
    private var tvDurationSize: TextView? = null
    private var btnBackToTop: View? = null

    // TV系列组件
    private var tvSeriesSection: LinearLayout? = null
    private var spinnerSeason: android.widget.Spinner? = null
    private var rvEpisodes: RecyclerView? = null
    private var episodeAdapter: EpisodeAdapter? = null

    companion object {
        private const val ARG_MEDIA_ITEM = "media_item"

        fun newInstance(mediaItem: MediaItem): VideoDetailsFragment {
            val fragment = VideoDetailsFragment()
            val args = Bundle()
            args.putParcelable(ARG_MEDIA_ITEM, mediaItem)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mediaItem = arguments?.getParcelable(ARG_MEDIA_ITEM) ?: run {
            android.util.Log.e("VideoDetailsFragment", "MediaItem is null in arguments")
            requireActivity().finish()
            return
        }
        android.util.Log.d("VideoDetailsFragment", "Fragment created with MediaItem: $mediaItem")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_video_details, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        setupData()
        setupListeners(view)
        observeViewModel()
        
        // 加载详情数据
        viewModel.loadVideoDetails(mediaItem)

        // 立即检查并设置TV系列UI（因为MediaItem已经可用）
        setupTVSeriesUI()
    }

    private fun initViews(view: View) {
        ivBackdrop = view.findViewById(R.id.iv_backdrop)
        tvTitle = view.findViewById(R.id.tv_title)
        btnPlay = view.findViewById(R.id.btn_play)
        tvRating = view.findViewById(R.id.tv_rating)
        tvYear = view.findViewById(R.id.tv_year)
        tvDuration = view.findViewById(R.id.tv_duration)
        tvGenre = view.findViewById(R.id.tv_genre)
        tvFileSize = view.findViewById(R.id.tv_file_size)
        tvOverview = view.findViewById(R.id.tv_overview)
        movieInfoContainer = view.findViewById(R.id.movie_info_container)
        scrollView = view.findViewById(R.id.scroll_view)
        scrollOverlay = view.findViewById(R.id.scroll_overlay)

        // 计算第一屏高度的像素值
        firstScreenHeightPx = (FIRST_SCREEN_HEIGHT * resources.displayMetrics.density).toInt()

        // 初始化演员表相关组件（可选的，因为可能不存在）
        try {
            rvActors = view.findViewById(R.id.rv_actors)
            tvFilename = view.findViewById(R.id.tv_filename)
            tvSourcePath = view.findViewById(R.id.tv_source_path)
            tvDurationSize = view.findViewById(R.id.tv_duration_size)
            btnBackToTop = view.findViewById(R.id.btn_back_to_top)
        } catch (e: Exception) {
            // 如果找不到这些组件，说明布局没有包含演员表部分
        }

        // 初始化TV系列组件
        try {
            tvSeriesSection = view.findViewById(R.id.tv_series_section)
            spinnerSeason = view.findViewById(R.id.spinner_season)
            rvEpisodes = view.findViewById(R.id.rv_episodes)
            android.util.Log.d("VideoDetailsFragment", "TV series components initialized: " +
                "tvSeriesSection=${tvSeriesSection != null}, " +
                "spinnerSeason=${spinnerSeason != null}, " +
                "rvEpisodes=${rvEpisodes != null}")
        } catch (e: Exception) {
            android.util.Log.e("VideoDetailsFragment", "Failed to initialize TV series components", e)
        }
    }

    private fun setupData() {
        // 设置基本信息
        tvTitle.text = mediaItem.getDisplayTitle()
        
        // 设置评分
        if (mediaItem.rating > 0) {
            tvRating.text = String.format("%.1f", mediaItem.rating)
            tvRating.visibility = View.VISIBLE
        } else {
            tvRating.visibility = View.GONE
        }
        
        // 设置年份（显示完整日期格式）
        mediaItem.releaseDate?.let { date ->
            val formatter = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
            tvYear.text = formatter.format(date)
        } ?: run {
            tvYear.text = "未知日期"
        }
        
        // 设置时长（TV系列显示 总X集（库中有Y集），电影显示时长）
        if (viewModel.isTVSeries()) {
            val total = viewModel.tmdbTotalEpisodes.value ?: 0
            val local = viewModel.localAvailableEpisodes.value ?: 0
            tvDuration.text = "总${total}集（库中有${local}集）"
            tvDuration.visibility = View.VISIBLE
        } else {
            if (mediaItem.duration > 0) {
                val hours = mediaItem.duration / 3600
                val minutes = (mediaItem.duration % 3600) / 60
                tvDuration.text = if (hours > 0) {
                    "${hours}小时${minutes}分钟"
                } else {
                    "${minutes}分钟"
                }
            } else {
                tvDuration.visibility = View.GONE
            }
        }
        
        // 设置类型
        if (mediaItem.genre.isNotEmpty()) {
            tvGenre.text = mediaItem.genre.joinToString(" · ")
        } else {
            tvGenre.text = when (mediaItem.mediaType) {
                com.tvplayer.webdav.data.model.MediaType.MOVIE -> "电影"
                com.tvplayer.webdav.data.model.MediaType.TV_EPISODE -> "电视剧"
                com.tvplayer.webdav.data.model.MediaType.TV_SERIES -> "电视剧"
                else -> "视频"
            }
        }
        
        // 设置文件大小
        if (mediaItem.fileSize > 0) {
            val sizeInGB = mediaItem.fileSize / (1024.0 * 1024.0 * 1024.0)
            tvFileSize.text = String.format("%.1f GB", sizeInGB)
        } else {
            tvFileSize.visibility = View.GONE
        }
        
        // 设置简介
        if (!mediaItem.overview.isNullOrEmpty()) {
            tvOverview.text = mediaItem.overview
        } else {
            tvOverview.text = "暂无简介"
        }
        
        // 加载背景图片
        loadBackdropImage()
        
        // 设置演员表（如果存在）
        setupActorsIfAvailable()

        // 设置视频详情信息（如果存在）
        setupVideoDetailsIfAvailable()

        // 为演员表设置按键监听
        setupActorsKeyListener()

        // 观察播放状态变化，动态更新视频详情
        setupPlaybackStateObserver()
    }

    private fun loadBackdropImage() {
        val backdropUrl = mediaItem.backdropPath ?: mediaItem.posterPath
        if (!backdropUrl.isNullOrEmpty()) {
            try {
                com.bumptech.glide.Glide.with(this)
                    .load(backdropUrl)
                    .centerCrop()
                    .into(ivBackdrop)
            } catch (e: Exception) {
                // 加载失败时使用默认图片
                ivBackdrop.setImageResource(R.drawable.ic_video)
            }
        } else {
            ivBackdrop.setImageResource(R.drawable.ic_video)
        }
    }

    private fun setupListeners(view: View) {
        // 播放按钮点击
        btnPlay.setOnClickListener {
            startPlayback()
        }

        // 返回顶部按钮点击（如果存在）
        btnBackToTop?.setOnClickListener {
            scrollToTop()
        }

        // 设置播放按钮焦点效果
        btnPlay.setOnFocusChangeListener { view, hasFocus ->
            if (hasFocus) {
                view.animate()
                    .scaleX(1.1f)
                    .scaleY(1.1f)
                    .setDuration(200)
                    .start()
            } else {
                view.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(200)
                    .start()
            }
        }

        // 设置返回顶部按钮焦点效果（如果存在）
        btnBackToTop?.setOnFocusChangeListener { view, hasFocus ->
            if (hasFocus) {
                // 获取焦点时的动画：放大并增强阴影效果
                view.animate()
                    .scaleX(1.08f)
                    .scaleY(1.08f)
                    .translationY(-2f)
                    .setDuration(250)
                    .setInterpolator(android.view.animation.OvershootInterpolator(0.8f))
                    .withStartAction {
                        // 添加轻微的透明度变化
                        view.alpha = 0.9f
                        view.animate().alpha(1.0f).setDuration(150).start()
                    }
                    .start()
            } else {
                // 失去焦点时的动画：平滑恢复原状
                view.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .translationY(0f)
                    .setDuration(200)
                    .setInterpolator(android.view.animation.DecelerateInterpolator(1.2f))
                    .start()
            }
        }
        
        // 设置返回顶部按钮点击效果（如果存在）
        btnBackToTop?.setOnTouchListener { view, event ->
            when (event.action) {
                android.view.MotionEvent.ACTION_DOWN -> {
                    // 按下时的反馈动画
                    view.animate()
                        .scaleX(0.95f)
                        .scaleY(0.95f)
                        .setDuration(100)
                        .start()
                    false
                }
                android.view.MotionEvent.ACTION_UP, 
                android.view.MotionEvent.ACTION_CANCEL -> {
                    // 释放时的恢复动画
                    view.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(150)
                        .setInterpolator(android.view.animation.OvershootInterpolator(1.5f))
                        .start()
                    false
                }
                else -> false
            }
        }
        
        // 设置返回顶部按钮点击事件（如果存在）
        btnBackToTop?.setOnClickListener {
            // 滚动到顶部的动画
            scrollView.smoothScrollTo(0, 0)
            
            // 添加视觉反馈
            it.animate()
                .rotation(360f)
                .setDuration(600)
                .withEndAction {
                    it.rotation = 0f
                }
                .start()
                
            // 显示提示信息
            android.widget.Toast.makeText(context, "已返回顶部", android.widget.Toast.LENGTH_SHORT).show()
        }



        // 设置滚动监听器来处理动态遮罩效果
        setupScrollListener()

        // 设置按键监听器来处理遥控器滚动
        setupKeyListener(view)

        // 默认焦点设置到播放按钮
        btnPlay.requestFocus()
    }

    private fun observeViewModel() {
        // 观察演员数据变化
        viewModel.actors.observe(viewLifecycleOwner) { actors ->
            setupActors(actors)
        }

        // 观察媒体项目数据变化
        viewModel.mediaItem.observe(viewLifecycleOwner) { updatedMediaItem ->
            android.util.Log.d("VideoDetailsFragment", "MediaItem observer triggered: $updatedMediaItem")
            updatedMediaItem?.let { newMediaItem ->
                // 更新当前的mediaItem引用，以便setupVideoDetailsIfAvailable使用最新数据
                mediaItem = newMediaItem

                // 更新UI显示的媒体项目信息
                updateMediaItemDisplay(newMediaItem)
                // 设置TV系列相关UI（在MediaItem数据加载后）
                setupTVSeriesUI()
                // 重新设置视频详情信息（使用更新后的MediaItem）
                setupVideoDetailsIfAvailable()

                // 刷新基于集数的显示（例如 总X集（库中有Y集））
                if (viewModel.isTVSeries()) {
                    val total = viewModel.tmdbTotalEpisodes.value ?: 0
                    val local = viewModel.localAvailableEpisodes.value ?: 0
                    tvDuration.text = "总${total}集（库中有${local}集）"
                    // Note: tvDurationSize is now handled by setupVideoDetailsIfAvailable() to show episode duration/size
                }
            }
        }

        // 观察集数计数变化，实时刷新显示
        viewModel.tmdbTotalEpisodes.observe(viewLifecycleOwner) { total ->
            if (viewModel.isTVSeries()) {
                val local = viewModel.localAvailableEpisodes.value ?: 0
                tvDuration.text = "总${total}集（库中有${local}集）"
            }
        }
        viewModel.localAvailableEpisodes.observe(viewLifecycleOwner) { local ->
            if (viewModel.isTVSeries()) {
                val total = viewModel.tmdbTotalEpisodes.value ?: 0
                tvDuration.text = "总${total}集（库中有${local}集）"
            }
        }

        // 观察TV系列季数据变化
        viewModel.seasons.observe(viewLifecycleOwner) { seasons ->
            setupSeasonSpinner(seasons)
        }

        // 观察当前季变化
        viewModel.currentSeason.observe(viewLifecycleOwner) { seasonNumber ->
            updateSelectedSeason(seasonNumber)
        }

        // 观察剧集数据变化
        viewModel.episodes.observe(viewLifecycleOwner) { episodes ->
            setupEpisodes(episodes)
        }
    }

    private fun startPlayback() {
        // TODO: 启动视频播放器
        // 这里将来会启动PlayerActivity
        android.widget.Toast.makeText(context, "开始播放: ${mediaItem.getDisplayTitle()}", android.widget.Toast.LENGTH_SHORT).show()

        // 模拟播放器启动
        viewModel.startPlayback()
    }

    private fun setupActorsIfAvailable() {
        // 这个方法现在只初始化RecyclerView，不设置数据
        // 数据将通过ViewModel的LiveData来更新
        rvActors?.let { recyclerView ->
            // 设置RecyclerView
            recyclerView.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            // 初始设置为空列表
            val actorAdapter = ActorAdapter(emptyList()) { actor ->
                // 处理演员点击事件
                android.widget.Toast.makeText(context, "点击了: ${actor.name}", android.widget.Toast.LENGTH_SHORT).show()
            }
            recyclerView.adapter = actorAdapter
        }
    }
    
    private fun setupActors(actors: List<Actor>) {
        rvActors?.let { recyclerView ->
            val adapter = recyclerView.adapter as? ActorAdapter
            if (adapter != null) {
                // 更新现有适配器的数据
                adapter.updateActors(actors)
            } else {
                // 创建新的适配器
                recyclerView.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                val actorAdapter = ActorAdapter(actors) { actor ->
                    // 处理演员点击事件
                    android.widget.Toast.makeText(context, "点击了: ${actor.name}", android.widget.Toast.LENGTH_SHORT).show()
                }
                recyclerView.adapter = actorAdapter
            }
        }
    }
    
    private fun updateMediaItemDisplay(updatedMediaItem: MediaItem) {
        // 更新标题
        tvTitle.text = updatedMediaItem.getDisplayTitle()
        
        // 更新评分
        if (updatedMediaItem.rating > 0) {
            tvRating.text = String.format("%.1f", updatedMediaItem.rating)
            tvRating.visibility = View.VISIBLE
        } else {
            tvRating.visibility = View.GONE
        }
        
        // 更新年份
        updatedMediaItem.releaseDate?.let { date ->
            val formatter = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
            tvYear.text = formatter.format(date)
        } ?: run {
            tvYear.text = "未知日期"
        }
        
        // 更新时长（TV系列显示 总X集（库中有Y集），电影显示时长）
        if (viewModel.isTVSeries()) {
            val total = viewModel.tmdbTotalEpisodes.value ?: 0
            val local = viewModel.localAvailableEpisodes.value ?: 0
            tvDuration.text = "总${total}集（库中有${local}集）"
            tvDuration.visibility = View.VISIBLE
        } else {
            if (updatedMediaItem.duration > 0) {
                val hours = updatedMediaItem.duration / 3600
                val minutes = (updatedMediaItem.duration % 3600) / 60
                tvDuration.text = if (hours > 0) {
                    "${hours}小时${minutes}分钟"
                } else {
                    "${minutes}分钟"
                }
            } else {
                tvDuration.visibility = View.GONE
            }
        }
        
        // 更新类型
        if (updatedMediaItem.genre.isNotEmpty()) {
            tvGenre.text = updatedMediaItem.genre.joinToString(" · ")
        } else {
            tvGenre.text = when (updatedMediaItem.mediaType) {
                com.tvplayer.webdav.data.model.MediaType.MOVIE -> "电影"
                com.tvplayer.webdav.data.model.MediaType.TV_EPISODE -> "电视剧"
                com.tvplayer.webdav.data.model.MediaType.TV_SERIES -> "电视剧"
                else -> "视频"
            }
        }
        
        // 更新简介
        if (!updatedMediaItem.overview.isNullOrEmpty()) {
            tvOverview.text = updatedMediaItem.overview
        } else {
            tvOverview.text = "暂无简介"
        }
        
        // 更新背景图片
        val backdropUrl = updatedMediaItem.backdropPath ?: updatedMediaItem.posterPath
        if (!backdropUrl.isNullOrEmpty()) {
            try {
                com.bumptech.glide.Glide.with(this)
                    .load(backdropUrl)
                    .centerCrop()
                    .into(ivBackdrop)
            } catch (e: Exception) {
                // 加载失败时使用默认图片
                ivBackdrop.setImageResource(R.drawable.ic_video)
            }
        } else {
            ivBackdrop.setImageResource(R.drawable.ic_video)
        }
    }

    private fun setupVideoDetailsIfAvailable() {
        // 检查是否为TV系列
        val isTVSeries = mediaItem.mediaType == com.tvplayer.webdav.data.model.MediaType.TV_EPISODE ||
                        mediaItem.mediaType == com.tvplayer.webdav.data.model.MediaType.TV_SERIES

        // 对于TV系列，获取当前播放剧集的文件信息来显示；对于电影，使用当前MediaItem
        val displayMediaItem = if (isTVSeries) {
            getCurrentlyPlayingEpisodeForDisplay() ?: mediaItem
        } else {
            mediaItem
        }

        android.util.Log.d("VideoDetailsFragment", "setupVideoDetailsIfAvailable: isTVSeries=$isTVSeries, using filePath: ${displayMediaItem.filePath}")

        // 设置文件名和技术信息（从filePath中提取文件名）
        tvFilename?.let { textView ->
            val fileName = try {
                val path = decodeFilePath(displayMediaItem.filePath)
                if (path.isNotBlank()) {
                    val fullFileName = path.substringAfterLast('/')
                    if (fullFileName.isNotBlank()) {
                        fullFileName
                    } else {
                        "未知文件"
                    }
                } else {
                    "未知文件"
                }
            } catch (e: Exception) {
                "未知文件"
            }
            textView.text = fileName
            textView.visibility = View.VISIBLE
        }

        // 设置来源路径
        tvSourcePath?.let { textView ->
            val sourcePath = try {
                val path = decodeFilePath(displayMediaItem.filePath)
                if (path.isNotBlank()) {
                    val lastSlashIndex = path.lastIndexOf('/')
                    if (lastSlashIndex > 0) {
                        // 格式化显示路径，添加前缀说明
                        val directory = path.substring(0, lastSlashIndex + 1)
                        "WebDAV: $directory"
                    } else {
                        "WebDAV: $path"
                    }
                } else {
                    "未知路径"
                }
            } catch (e: Exception) {
                "未知路径"
            }
            textView.text = sourcePath
            textView.visibility = View.VISIBLE
        }
        
        // 设置时长和大小信息（对于TV系列和电影都使用相同的格式）
        tvDurationSize?.let { textView ->
            android.util.Log.d("VideoDetailsFragment", "=== Setting Duration/Size Info ===")
            android.util.Log.d("VideoDetailsFragment", "displayMediaItem.duration: ${displayMediaItem.duration}")
            android.util.Log.d("VideoDetailsFragment", "displayMediaItem.fileSize: ${displayMediaItem.fileSize}")
            android.util.Log.d("VideoDetailsFragment", "displayMediaItem.title: ${displayMediaItem.title}")
            android.util.Log.d("VideoDetailsFragment", "displayMediaItem.filePath: ${displayMediaItem.filePath}")

            val durationText = if (displayMediaItem.duration > 0) {
                val hours = displayMediaItem.duration / 3600
                val minutes = (displayMediaItem.duration % 3600) / 60
                if (hours > 0) "${hours}小时${minutes}分钟" else "${minutes}分钟"
            } else {
                android.util.Log.w("VideoDetailsFragment", "Duration is 0 or negative: ${displayMediaItem.duration}")
                "未知时长"
            }

            val sizeText = if (displayMediaItem.fileSize > 0) {
                val sizeInGB = displayMediaItem.fileSize / (1024.0 * 1024.0 * 1024.0)
                String.format("%.2f GB", sizeInGB)
            } else {
                android.util.Log.w("VideoDetailsFragment", "File size is 0 or negative: ${displayMediaItem.fileSize}")
                "未知大小"
            }

            // 检测视频分辨率
            val resolutionText = detectVideoResolution(displayMediaItem)
            android.util.Log.d("VideoDetailsFragment", "Detected resolution: $resolutionText")

            val finalText = "$durationText $resolutionText $sizeText"
            android.util.Log.d("VideoDetailsFragment", "Setting tvDurationSize text to: '$finalText'")
            textView.text = finalText
            textView.visibility = View.VISIBLE

            // Verify the text was actually set
            android.util.Log.d("VideoDetailsFragment", "tvDurationSize actual text after setting: '${textView.text}'")
        }
    }

    /**
     * 获取当前播放剧集的MediaItem用于显示文件信息
     * 优先返回当前播放的剧集，如果没有播放状态则返回第一集
     */
    private fun getCurrentlyPlayingEpisodeForDisplay(): MediaItem? {
        return try {
            // 从ViewModel获取当前系列的所有剧集
            val seriesId = mediaItem.seriesId
            if (seriesId.isNullOrBlank()) {
                android.util.Log.w("VideoDetailsFragment", "No seriesId found for TV series")
                return null
            }

            // 从MediaCache获取所有媒体项目
            val allItems = viewModel.getAllMediaItems()

            // 找到同一系列的所有剧集
            val seriesEpisodes = allItems.filter { item ->
                item.seriesId == seriesId &&
                item.mediaType == com.tvplayer.webdav.data.model.MediaType.TV_EPISODE
            }

            android.util.Log.d("VideoDetailsFragment", "Found ${seriesEpisodes.size} episodes for series: $seriesId")
            seriesEpisodes.forEach { episode ->
                android.util.Log.d("VideoDetailsFragment", "Episode: S${episode.seasonNumber}E${episode.episodeNumber}")
                android.util.Log.d("VideoDetailsFragment", "  - Title: ${episode.title}")
                android.util.Log.d("VideoDetailsFragment", "  - Duration: ${episode.duration}s")
                android.util.Log.d("VideoDetailsFragment", "  - File Size: ${episode.fileSize} bytes")
                android.util.Log.d("VideoDetailsFragment", "  - File Path: ${episode.filePath}")
            }

            if (seriesEpisodes.isEmpty()) {
                android.util.Log.w("VideoDetailsFragment", "No episodes found for series: $seriesId")
                return null
            }

            // 检查是否有播放状态
            val playbackState = viewModel.getPlaybackState(seriesId)
            val targetEpisode = if (playbackState != null) {
                // 找到当前播放的剧集
                seriesEpisodes.find { episode ->
                    episode.seasonNumber == playbackState.currentSeasonNumber &&
                    episode.episodeNumber == playbackState.currentEpisodeNumber
                }.also {
                    android.util.Log.d("VideoDetailsFragment", "Found currently playing episode: S${playbackState.currentSeasonNumber}E${playbackState.currentEpisodeNumber}, path: ${it?.filePath}")
                }
            } else {
                // 没有播放状态，返回第一集
                seriesEpisodes.sortedWith(
                    compareBy<MediaItem> { it.seasonNumber ?: 1 }
                        .thenBy { it.episodeNumber ?: 1 }
                ).firstOrNull().also {
                    android.util.Log.d("VideoDetailsFragment", "No playback state found, using first episode: S${it?.seasonNumber}E${it?.episodeNumber}, path: ${it?.filePath}")
                }
            }

            targetEpisode
        } catch (e: Exception) {
            android.util.Log.e("VideoDetailsFragment", "Error getting currently playing episode", e)
            null
        }
    }

    /**
     * 设置播放状态观察者，当播放状态变化时动态更新视频详情
     */
    private fun setupPlaybackStateObserver() {
        // 只对TV系列设置播放状态观察者
        val isTVSeries = mediaItem.mediaType == com.tvplayer.webdav.data.model.MediaType.TV_EPISODE ||
                        mediaItem.mediaType == com.tvplayer.webdav.data.model.MediaType.TV_SERIES

        if (isTVSeries) {
            viewModel.getCurrentPlaybackState().observe(viewLifecycleOwner) { playbackState ->
                android.util.Log.d("VideoDetailsFragment", "Playback state changed: ${playbackState?.getEpisodeIdentifier()}")

                // 当播放状态变化时，重新设置视频详情信息
                if (playbackState != null && playbackState.seriesId == mediaItem.seriesId) {
                    android.util.Log.d("VideoDetailsFragment", "Updating video details for playback state change")
                    setupVideoDetailsIfAvailable()
                }
            }
        }
    }

    /**
     * 开始播放指定剧集（用于测试和实际播放）
     */
    fun startPlaybackForEpisode(seasonNumber: Int, episodeNumber: Int, duration: Long = 0L) {
        val seriesId = mediaItem.seriesId
        if (!seriesId.isNullOrBlank()) {
            android.util.Log.d("VideoDetailsFragment", "Starting playback for S${seasonNumber}E${episodeNumber}")
            viewModel.startPlayback(seriesId, seasonNumber, episodeNumber, duration)
        }
    }

    /**
     * 更新播放进度（用于实际播放器集成）
     */
    fun updatePlaybackProgress(progress: Long, duration: Long = 0L) {
        val seriesId = mediaItem.seriesId
        if (!seriesId.isNullOrBlank()) {
            viewModel.updatePlaybackProgress(seriesId, progress, duration)
        }
    }

    /**
     * 切换到下一集
     */
    fun switchToNextEpisode(nextSeasonNumber: Int, nextEpisodeNumber: Int) {
        val seriesId = mediaItem.seriesId
        if (!seriesId.isNullOrBlank()) {
            android.util.Log.d("VideoDetailsFragment", "Switching to next episode: S${nextSeasonNumber}E${nextEpisodeNumber}")
            viewModel.switchToNextEpisode(seriesId, nextSeasonNumber, nextEpisodeNumber)
        }
    }

    /**
     * 测试播放状态功能（可以通过按键或其他方式触发）
     */
    private fun testPlaybackStateFeature() {
        val seriesId = mediaItem.seriesId
        if (!seriesId.isNullOrBlank()) {
            // 模拟播放第2季第3集
            android.util.Log.d("VideoDetailsFragment", "Testing playback state: switching to S02E03")
            startPlaybackForEpisode(2, 3, 3600L) // 1小时时长

            // 模拟播放进度到30分钟
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                updatePlaybackProgress(1800L, 3600L) // 30分钟进度
                android.util.Log.d("VideoDetailsFragment", "Updated playback progress to 30 minutes")
            }, 2000)
        }
    }

    /**
     * 从MediaItem中检测视频分辨率 - 优化版本
     * 基于行业标准和真实世界的视频文件命名约定
     */
    private fun detectVideoResolution(mediaItem: MediaItem): String {
        return try {
            val filePath = mediaItem.filePath.lowercase()
            val fileName = filePath.substringAfterLast('/').substringBeforeLast('.')
            val fullPath = filePath.lowercase()

            android.util.Log.d("VideoDetailsFragment", "Detecting resolution for: $fileName")
            android.util.Log.d("VideoDetailsFragment", "Full path: $fullPath")

            // 第一阶段：直接分辨率标识符检测（最高优先级）
            val directResolution = detectDirectResolutionMarkers(fileName, fullPath)
            if (directResolution != null) {
                android.util.Log.d("VideoDetailsFragment", "Direct resolution detected: $directResolution")
                return directResolution
            }

            // 第二阶段：质量标签推断
            val qualityResolution = detectResolutionFromQualityTags(fileName, fullPath)
            if (qualityResolution != null) {
                android.util.Log.d("VideoDetailsFragment", "Quality-based resolution detected: $qualityResolution")
                return qualityResolution
            }

            // 第三阶段：正则表达式模式匹配
            val regexResolution = detectResolutionFromRegexPatterns(fileName, fullPath)
            if (regexResolution != null) {
                android.util.Log.d("VideoDetailsFragment", "Regex-based resolution detected: $regexResolution")
                return regexResolution
            }

            // 第四阶段：文件大小和时长推断
            val inferredResolution = inferResolutionFromFileMetadata(mediaItem)
            if (inferredResolution != null) {
                android.util.Log.d("VideoDetailsFragment", "Inferred resolution from metadata: $inferredResolution")
                return inferredResolution
            }

            android.util.Log.d("VideoDetailsFragment", "No resolution pattern found, defaulting to 其他")
            "其他"

        } catch (e: Exception) {
            android.util.Log.e("VideoDetailsFragment", "Error detecting video resolution", e)
            "其他"
        }
    }

    /**
     * 第一阶段：检测直接分辨率标识符
     */
    private fun detectDirectResolutionMarkers(fileName: String, fullPath: String): String? {
        // 4K/UHD 检测 - 最全面的4K标识符
        val fourKPatterns = listOf(
            "4k", "2160p", "uhd", "ultra.hd", "ultrahd", "uhdtv", "4k.uhd",
            "3840x2160", "4096x2160", "2160", "uhd.4k", "4k.2160p"
        )

        // 2K/QHD 检测
        val twoKPatterns = listOf(
            "2k", "1440p", "qhd", "quad.hd", "quadhd", "wqhd",
            "2560x1440", "2048x1080", "1440", "2k.qhd"
        )

        // 1080P/FHD 检测 - 包含各种变体
        val fullHDPatterns = listOf(
            "1080p", "1080i", "fhd", "full.hd", "fullhd", "1920x1080",
            "1080", "fhd.1080p", "bluray.1080p", "web.1080p"
        )

        // 720P/HD 检测
        val hdPatterns = listOf(
            "720p", "720i", "hd", "1280x720", "720", "hd.720p",
            "web.720p", "hdtv.720p"
        )

        // 检查4K模式
        for (pattern in fourKPatterns) {
            if (fileName.contains(pattern) || fullPath.contains(pattern)) {
                return "4K"
            }
        }

        // 检查2K模式
        for (pattern in twoKPatterns) {
            if (fileName.contains(pattern) || fullPath.contains(pattern)) {
                return "2K"
            }
        }

        // 检查1080P模式
        for (pattern in fullHDPatterns) {
            if (fileName.contains(pattern) || fullPath.contains(pattern)) {
                return "1080P"
            }
        }

        // 检查720P模式
        for (pattern in hdPatterns) {
            if (fileName.contains(pattern) || fullPath.contains(pattern)) {
                return "720P"
            }
        }

        return null
    }

    /**
     * 第二阶段：从质量标签推断分辨率
     */
    private fun detectResolutionFromQualityTags(fileName: String, fullPath: String): String? {
        // 4K质量标签 - 通常与4K内容相关
        val fourKQualityTags = listOf(
            "remux", "bdremux", "uhd.remux", "4k.remux", "atmos", "dv", "dolby.vision",
            "hdr10", "hdr", "imax", "criterion", "masters"
        )

        // 1080P质量标签 - 高质量但通常是1080P
        val fullHDQualityTags = listOf(
            "bluray", "blu.ray", "bdrip", "brip", "brrip", "web.dl", "webdl", "webrip",
            "netflix", "amazon", "hulu", "disney", "hbo", "apple.tv", "paramount"
        )

        // 720P质量标签 - 中等质量
        val hdQualityTags = listOf(
            "hdtv", "hdcam", "hdts", "hdtc", "web.720p", "iptv"
        )

        // 低质量标签 - 通常是720P或更低
        val standardQualityTags = listOf(
            "dvdrip", "dvd.rip", "dvdscr", "cam", "ts", "tc", "workprint", "r5", "r6"
        )

        // 检查4K质量标签
        for (tag in fourKQualityTags) {
            if (fileName.contains(tag) || fullPath.contains(tag)) {
                // 如果有4K质量标签但没有明确的分辨率，推断为4K
                return "4K"
            }
        }

        // 检查1080P质量标签
        for (tag in fullHDQualityTags) {
            if (fileName.contains(tag) || fullPath.contains(tag)) {
                // 如果有高质量标签但没有明确分辨率，推断为1080P
                return "1080P"
            }
        }

        // 检查720P质量标签
        for (tag in hdQualityTags) {
            if (fileName.contains(tag) || fullPath.contains(tag)) {
                return "720P"
            }
        }

        // 检查标准质量标签
        for (tag in standardQualityTags) {
            if (fileName.contains(tag) || fullPath.contains(tag)) {
                return "720P" // 大多数DVD rips是720P或更低
            }
        }

        return null
    }

    /**
     * 第三阶段：正则表达式模式匹配
     */
    private fun detectResolutionFromRegexPatterns(fileName: String, fullPath: String): String? {
        // 增强的正则表达式模式
        val patterns = listOf(
            // 标准分辨率格式: 1920x1080, 3840x2160等
            Regex("(\\d{3,4})\\s*[x×]\\s*(\\d{3,4})"),
            // P格式: 1080p, 720p, 2160p等
            Regex("(\\d{3,4})p", RegexOption.IGNORE_CASE),
            // I格式: 1080i, 720i等
            Regex("(\\d{3,4})i", RegexOption.IGNORE_CASE),
            // 带点分隔: 1920.1080, 3840.2160
            Regex("(\\d{3,4})\\.\\s*(\\d{3,4})"),
            // 带下划线: 1920_1080
            Regex("(\\d{3,4})_\\s*(\\d{3,4})"),
            // 方括号格式: [1080p], [720p]
            Regex("\\[(\\d{3,4})[pi]\\]", RegexOption.IGNORE_CASE),
            // 圆括号格式: (1080p), (720p)
            Regex("\\((\\d{3,4})[pi]\\)", RegexOption.IGNORE_CASE)
        )

        for (pattern in patterns) {
            val match = pattern.find(fileName) ?: pattern.find(fullPath)
            if (match != null) {
                val width: Int
                val height: Int

                when {
                    // 宽x高格式
                    match.groupValues.size >= 3 && match.groupValues[1].isNotEmpty() && match.groupValues[2].isNotEmpty() -> {
                        width = match.groupValues[1].toIntOrNull() ?: 0
                        height = match.groupValues[2].toIntOrNull() ?: 0
                    }
                    // 单一数字格式 (p/i)
                    match.groupValues.size >= 2 && match.groupValues[1].isNotEmpty() -> {
                        height = match.groupValues[1].toIntOrNull() ?: 0
                        width = when (height) {
                            2160 -> 3840
                            1440 -> 2560
                            1080 -> 1920
                            720 -> 1280
                            576 -> 720  // PAL
                            480 -> 640  // NTSC
                            else -> 0
                        }
                    }
                    else -> {
                        width = 0
                        height = 0
                    }
                }

                android.util.Log.d("VideoDetailsFragment", "Regex extracted resolution: ${width}x${height}")

                // 分类分辨率
                return categorizeResolution(width, height)
            }
        }

        return null
    }

    /**
     * 第四阶段：从文件元数据推断分辨率
     */
    private fun inferResolutionFromFileMetadata(mediaItem: MediaItem): String? {
        val fileSize = mediaItem.fileSize
        val duration = mediaItem.duration

        if (fileSize <= 0 || duration <= 0) {
            return null
        }

        // 计算比特率 (bytes per second)
        val bitrate = fileSize.toDouble() / duration.toDouble()

        // 基于文件大小和时长的启发式推断
        // 这些值基于典型的视频编码比特率
        return when {
            // 4K内容通常有很高的比特率
            bitrate > 2_000_000 -> { // > 2MB/s
                android.util.Log.d("VideoDetailsFragment", "High bitrate detected (${String.format("%.2f", bitrate / 1_000_000)} MB/s), inferring 4K")
                "4K"
            }
            // 1080P内容的典型比特率
            bitrate > 800_000 -> { // > 800KB/s
                android.util.Log.d("VideoDetailsFragment", "Medium-high bitrate detected (${String.format("%.2f", bitrate / 1_000_000)} MB/s), inferring 1080P")
                "1080P"
            }
            // 720P内容的典型比特率
            bitrate > 300_000 -> { // > 300KB/s
                android.util.Log.d("VideoDetailsFragment", "Medium bitrate detected (${String.format("%.2f", bitrate / 1_000_000)} MB/s), inferring 720P")
                "720P"
            }
            else -> {
                android.util.Log.d("VideoDetailsFragment", "Low bitrate detected (${String.format("%.2f", bitrate / 1_000_000)} MB/s), cannot infer resolution")
                null
            }
        }
    }

    /**
     * 辅助方法：根据宽度和高度分类分辨率
     */
    private fun categorizeResolution(width: Int, height: Int): String? {
        return when {
            height >= 2160 || width >= 3840 -> "4K"
            height >= 1440 || width >= 2560 -> "2K"
            height >= 1080 || width >= 1920 -> "1080P"
            height >= 720 || width >= 1280 -> "720P"
            height >= 576 || width >= 720 -> "720P"  // PAL标准
            height >= 480 || width >= 640 -> "720P"  // NTSC标准，归类为720P
            else -> null
        }
    }

    /**
     * 测试视频详情显示功能
     */
    private fun testVideoDetailsDisplay() {
        val seriesId = mediaItem.seriesId
        if (!seriesId.isNullOrBlank()) {
            android.util.Log.d("VideoDetailsFragment", "=== Testing Video Details Display ===")
            android.util.Log.d("VideoDetailsFragment", "Series ID: $seriesId")
            android.util.Log.d("VideoDetailsFragment", "Original MediaItem: ${mediaItem.title}")
            android.util.Log.d("VideoDetailsFragment", "Original duration: ${mediaItem.duration}s, fileSize: ${mediaItem.fileSize} bytes")

            val currentEpisode = getCurrentlyPlayingEpisodeForDisplay()
            if (currentEpisode != null) {
                android.util.Log.d("VideoDetailsFragment", "Current Episode: S${currentEpisode.seasonNumber}E${currentEpisode.episodeNumber}")
                android.util.Log.d("VideoDetailsFragment", "Episode duration: ${currentEpisode.duration}s, fileSize: ${currentEpisode.fileSize} bytes")
                android.util.Log.d("VideoDetailsFragment", "Episode file path: ${currentEpisode.filePath}")

                // Test resolution detection
                val detectedResolution = detectVideoResolution(currentEpisode)
                android.util.Log.d("VideoDetailsFragment", "Detected resolution: $detectedResolution")
            } else {
                android.util.Log.w("VideoDetailsFragment", "No current episode found!")
            }

            // Check if tvDurationSize exists
            android.util.Log.d("VideoDetailsFragment", "tvDurationSize exists: ${tvDurationSize != null}")
            android.util.Log.d("VideoDetailsFragment", "Current tvDurationSize text: '${tvDurationSize?.text}'")

            // 刷新显示
            setupVideoDetailsIfAvailable()

            // Check again after refresh
            android.util.Log.d("VideoDetailsFragment", "After refresh tvDurationSize text: '${tvDurationSize?.text}'")
        }
    }

    /**
     * 测试分辨率检测功能 - 增强版本
     */
    private fun testResolutionDetection() {
        android.util.Log.d("VideoDetailsFragment", "=== Enhanced Resolution Detection Testing ===")

        // 测试各种真实世界的文件名格式
        val testCases = listOf(
            // 4K测试用例
            "Avengers.Endgame.2019.4K.UHD.2160p.BluRay.x265.HDR.mkv" to "4K",
            "The.Matrix.1999.UHD.BluRay.2160p.DTS-HD.MA.5.1.HEVC.REMUX.mkv" to "4K",
            "Dune.2021.3840x2160.HDR10.Dolby.Vision.mkv" to "4K",
            "Movie.4K.IMAX.Enhanced.mkv" to "4K",

            // 2K测试用例
            "Game.of.Thrones.S08E06.2K.QHD.1440p.WEB-DL.mkv" to "2K",
            "Film.2560x1440.WQHD.mp4" to "2K",

            // 1080P测试用例
            "Breaking.Bad.S01E01.1080p.BluRay.x264.mkv" to "1080P",
            "Movie.2023.FHD.1080p.WEB-DL.H264.mp4" to "1080P",
            "Series.1920x1080.Netflix.WEBRip.mkv" to "1080P",
            "Film.BRRip.1080p.x265.mp4" to "1080P",
            "Show.WEB.1080p.Amazon.Prime.mkv" to "1080P",
            "Content.BluRay.Remux.1080p.mkv" to "1080P",

            // 720P测试用例
            "Series.S01E01.720p.HDTV.x264.mkv" to "720P",
            "Movie.720p.WEB-DL.DD5.1.H264.mp4" to "720P",
            "Show.1280x720.HDTV.mkv" to "720P",
            "Film.DVDRip.720p.XviD.avi" to "720P",

            // 边缘情况
            "Movie.[1080p].BluRay.mkv" to "1080P",
            "Series.(720p).WEB-DL.mp4" to "720P",
            "Film_1920_1080_H264.mkv" to "1080P",
            "Show.1080i.HDTV.mkv" to "1080P",
            "Content.576p.DVDRip.avi" to "720P",

            // 质量标签推断测试
            "Movie.BluRay.Remux.No.Resolution.mkv" to "1080P",
            "Series.Netflix.WEBRip.Unknown.mkv" to "1080P",
            "Film.HDTV.Capture.mkv" to "720P",
            "Show.DVDRip.XviD.avi" to "720P",

            // 应该检测为"其他"的情况
            "Movie.CAM.LowQuality.avi" to "其他",
            "Series.Unknown.Format.mkv" to "其他"
        )

        var correctDetections = 0
        val totalTests = testCases.size

        testCases.forEach { (fileName, expectedResolution) ->
            // 创建测试用的MediaItem，包含不同的文件大小来测试推断逻辑
            val fileSize = when (expectedResolution) {
                "4K" -> 8_000_000_000L // 8GB
                "2K" -> 4_000_000_000L // 4GB
                "1080P" -> 2_000_000_000L // 2GB
                "720P" -> 1_000_000_000L // 1GB
                else -> 500_000_000L // 500MB
            }

            val testMediaItem = mediaItem.copy(
                filePath = "/test/path/$fileName",
                fileSize = fileSize,
                duration = 7200L // 2小时
            )

            val detectedResolution = detectVideoResolution(testMediaItem)
            val isCorrect = detectedResolution == expectedResolution

            if (isCorrect) correctDetections++

            val status = if (isCorrect) "✓" else "✗"
            android.util.Log.d("VideoDetailsFragment", "$status File: $fileName")
            android.util.Log.d("VideoDetailsFragment", "  Expected: $expectedResolution, Detected: $detectedResolution")
        }

        val accuracy = (correctDetections.toDouble() / totalTests.toDouble()) * 100
        android.util.Log.d("VideoDetailsFragment", "=== Test Results ===")
        android.util.Log.d("VideoDetailsFragment", "Correct: $correctDetections/$totalTests")
        android.util.Log.d("VideoDetailsFragment", "Accuracy: ${String.format("%.1f", accuracy)}%")

        // 测试当前剧集
        val currentEpisode = getCurrentlyPlayingEpisodeForDisplay()
        if (currentEpisode != null) {
            android.util.Log.d("VideoDetailsFragment", "=== Current Episode Test ===")
            val currentResolution = detectVideoResolution(currentEpisode)
            android.util.Log.d("VideoDetailsFragment", "Current episode resolution: $currentResolution")
            android.util.Log.d("VideoDetailsFragment", "Current episode file: ${currentEpisode.filePath}")
            android.util.Log.d("VideoDetailsFragment", "File size: ${currentEpisode.fileSize} bytes")
            android.util.Log.d("VideoDetailsFragment", "Duration: ${currentEpisode.duration} seconds")
        }
    }

    private fun scrollToTop() {
        scrollView.smoothScrollTo(0, 0)

        // 手动触发遮罩效果，确保返回顶部时遮罩正确隐藏
        updateScrollOverlay(0)
    }

    /**
     * 设置滚动监听器，实现动态遮罩效果
     * 当滚动到第二页/区域时，显示半透明遮罩层以提高文本可读性
     * 遵循Netflix/Disney+/Prime Video的UI模式，提供平滑的视觉过渡
     *
     * 透明度行为：
     * - 0-80% 第一屏高度：完全透明 (alpha = 0.0)，确保背景海报完全可见
     * - 80-100% 第一屏高度：渐变显示 (alpha 0.0 -> 0.85)
     * - 100%+ 第一屏高度：完全显示 (alpha = 0.85)，提供文本可读性
     */
    private fun setupScrollListener() {
        scrollView.viewTreeObserver.addOnScrollChangedListener {
            val scrollY = scrollView.scrollY
            val maxScrollForFirstScreen = firstScreenHeightPx

            // 计算滚动进度：0.0 (第一屏) 到 1.0 (第二屏及以后)
            val scrollProgress = (scrollY.toFloat() / maxScrollForFirstScreen).coerceIn(0.0f, 1.0f)

            // 修改透明度计算逻辑：前80%完全透明，80-100%渐变显示
            val alpha = when {
                scrollProgress < 0.8f -> 0.0f // 前80%完全透明，确保背景海报完全可见
                scrollProgress >= 1.0f -> 0.85f // 进入第二屏后完全显示遮罩
                else -> {
                    // 在 80%-100% 区间内使用平滑的渐变过渡
                    val normalizedProgress = (scrollProgress - 0.8f) / 0.2f // 将80%-100%映射到0-1
                    // 使用 smoothstep 函数创建平滑过渡
                    val smoothStep = normalizedProgress * normalizedProgress * (3.0f - 2.0f * normalizedProgress)
                    smoothStep * 0.85f
                }
            }

            // 应用透明度变化，使用动画来平滑过渡
            // 根据滚动方向调整动画时长，向下滚动时稍快，向上滚动时稍慢
            val animationDuration = if (scrollY > (scrollOverlay.tag as? Int ?: 0)) 80L else 120L
            scrollOverlay.tag = scrollY // 记录上次滚动位置

            scrollOverlay.animate()
                .alpha(alpha)
                .setDuration(animationDuration)
                .setInterpolator(android.view.animation.DecelerateInterpolator(1.5f))
                .start()
        }
    }

    private fun setupKeyListener(view: View) {
        // 为整个根视图设置按键监听
        view.isFocusableInTouchMode = true

        view.setOnKeyListener { _, keyCode, event ->
            if (event.action == KeyEvent.ACTION_DOWN) {
                when (keyCode) {
                    KeyEvent.KEYCODE_DPAD_DOWN -> {
                        // 检查当前焦点是否在播放按钮上
                        if (btnPlay.hasFocus()) {
                            handleDownKey()
                            true
                        } else {
                            false // 让其他控件处理
                        }
                    }
                    KeyEvent.KEYCODE_DPAD_UP -> {
                        // 任何时候按上键都直接回到顶部
                        val currentScrollY = scrollView.scrollY
                        if (currentScrollY > 50) { // 如果不在顶部
                            scrollToTop()
                            btnPlay.requestFocus()
                            true
                        } else {
                            false
                        }
                    }
                    KeyEvent.KEYCODE_MENU -> {
                        // 按菜单键测试播放状态功能（仅用于测试）
                        testPlaybackStateFeature()
                        true
                    }
                    KeyEvent.KEYCODE_GUIDE -> {
                        // 按指南键测试分辨率检测功能（仅用于测试）
                        testResolutionDetection()
                        true
                    }
                    else -> false
                }
            } else {
                false
            }
        }

        // 也为播放按钮单独设置按键监听
        btnPlay.setOnKeyListener { _, keyCode, event ->
            if (event.action == KeyEvent.ACTION_DOWN) {
                when (keyCode) {
                    KeyEvent.KEYCODE_DPAD_DOWN -> {
                        handleDownKey()
                        true
                    }
                    KeyEvent.KEYCODE_DPAD_UP -> {
                        // 播放按钮按上键时，如果不在顶部就滚动到顶部
                        val currentScrollY = scrollView.scrollY
                        if (currentScrollY > 50) {
                            scrollToTop()
                            true
                        } else {
                            false
                        }
                    }
                    else -> false
                }
            } else {
                false
            }
        }
    }

    private fun handleDownKey() {
        val currentScrollY = scrollView.scrollY

        // 如果当前在第一屏（滚动位置小于第一屏高度的一半）
        if (currentScrollY < firstScreenHeightPx / 2) {
            // 直接滚动到第二屏（演员表区域）
            scrollToSecondScreen()
        } else {
            // 如果已经在第二屏，继续正常滚动
            scrollView.smoothScrollBy(0, 200)
        }
    }

    private fun handleUpKey() {
        val currentScrollY = scrollView.scrollY

        // 如果当前滚动位置大于100px（说明不在顶部），直接滚动到顶部
        if (currentScrollY > 100) {
            // 直接滚动回第一屏顶部
            scrollToTop()
        } else {
            // 如果已经在第一屏顶部，不做任何操作或者可以退出页面
            // 这里可以添加退出逻辑，比如返回上一页
        }
    }

    private fun scrollToSecondScreen() {
        // 滚动到第二屏的开始位置（演员表区域）
        scrollView.smoothScrollTo(0, firstScreenHeightPx)

        // 手动触发遮罩效果，确保在程序化滚动时也能正确显示
        updateScrollOverlay(firstScreenHeightPx)
    }

    /**
     * 手动更新滚动遮罩效果
     * 用于程序化滚动时确保遮罩效果正确显示
     * 使用与自动滚动监听器相同的透明度逻辑
     */
    private fun updateScrollOverlay(scrollY: Int) {
        val maxScrollForFirstScreen = firstScreenHeightPx
        val scrollProgress = (scrollY.toFloat() / maxScrollForFirstScreen).coerceIn(0.0f, 1.0f)

        // 使用与setupScrollListener相同的透明度计算逻辑
        val alpha = when {
            scrollProgress < 0.8f -> 0.0f // 前80%完全透明
            scrollProgress >= 1.0f -> 0.85f // 进入第二屏后完全显示遮罩
            else -> {
                // 在 80%-100% 区间内使用平滑的渐变过渡
                val normalizedProgress = (scrollProgress - 0.8f) / 0.2f
                val smoothStep = normalizedProgress * normalizedProgress * (3.0f - 2.0f * normalizedProgress)
                smoothStep * 0.85f
            }
        }

        scrollOverlay.animate()
            .alpha(alpha)
            .setDuration(300) // 程序化滚动时使用稍长的动画时间
            .setInterpolator(android.view.animation.DecelerateInterpolator(1.5f))
            .start()
    }

    private fun setupActorsKeyListener() {
        rvActors?.setOnKeyListener { _, keyCode, event ->
            if (event.action == KeyEvent.ACTION_DOWN) {
                when (keyCode) {
                    KeyEvent.KEYCODE_DPAD_UP -> {
                        // 直接回到第一屏顶部，不管当前在演员表的哪个位置
                        scrollToTop()
                        // 将焦点设置回播放按钮
                        btnPlay.requestFocus()
                        true
                    }
                    else -> false
                }
            } else {
                false
            }
        }

        // 为返回顶部按钮设置按键监听
        btnBackToTop?.setOnKeyListener { _, keyCode, event ->
            if (event.action == KeyEvent.ACTION_DOWN) {
                when (keyCode) {
                    KeyEvent.KEYCODE_DPAD_UP -> {
                        scrollToTop()
                        // 将焦点设置回播放按钮
                        btnPlay.requestFocus()
                        true
                    }
                    else -> false
                }
            } else {
                false
            }
        }
    }

    /**
     * 解码文件路径，处理URL编码的中文字符
     */
    private fun decodeFilePath(filePath: String): String {
        return try {
            // 检查是否包含URL编码字符
            if (!filePath.contains("%")) {
                return filePath
            }

            // 首先尝试UTF-8解码
            var decoded = URLDecoder.decode(filePath, StandardCharsets.UTF_8.toString())

            // 如果还有编码字符，再次尝试解码（处理双重编码的情况）
            var previousDecoded = decoded
            while (decoded.contains("%")) {
                try {
                    decoded = URLDecoder.decode(decoded, StandardCharsets.UTF_8.toString())
                    // 如果解码后没有变化，说明无法进一步解码，跳出循环
                    if (decoded == previousDecoded) {
                        break
                    }
                    previousDecoded = decoded
                } catch (e: Exception) {
                    break
                }
            }

            decoded
        } catch (e: Exception) {
            // 如果UTF-8解码失败，尝试其他编码方式
            try {
                URLDecoder.decode(filePath, "GBK")
            } catch (e2: Exception) {
                try {
                    // 最后尝试ISO-8859-1
                    URLDecoder.decode(filePath, "ISO-8859-1")
                } catch (e3: Exception) {
                    // 如果都失败了，返回原始路径
                    filePath
                }
            }
        }
    }

    /**
     * 设置TV系列相关UI
     */
    private fun setupTVSeriesUI() {
        val isTVSeries = viewModel.isTVSeries()
        val directCheck = mediaItem.mediaType == com.tvplayer.webdav.data.model.MediaType.TV_EPISODE ||
                         mediaItem.mediaType == com.tvplayer.webdav.data.model.MediaType.TV_SERIES
        android.util.Log.d("VideoDetailsFragment", "setupTVSeriesUI: isTVSeries = $isTVSeries, directCheck = $directCheck, mediaType = ${mediaItem.mediaType}")

        if (isTVSeries || directCheck) {
            android.util.Log.d("VideoDetailsFragment", "Setting TV series section visible")
            tvSeriesSection?.visibility = View.VISIBLE
            setupEpisodeList()
        } else {
            android.util.Log.d("VideoDetailsFragment", "Setting TV series section gone")
            tvSeriesSection?.visibility = View.GONE
        }
    }

    /**
     * 设置剧集列表
     */
    private fun setupEpisodeList() {
        android.util.Log.d("VideoDetailsFragment", "setupEpisodeList called, rvEpisodes = ${rvEpisodes != null}")
        rvEpisodes?.let { recyclerView ->
            android.util.Log.d("VideoDetailsFragment", "Setting up episode adapter and layout manager")
            episodeAdapter = EpisodeAdapter(
                onEpisodeClick = { episode ->
                    // 播放对应的媒体文件
                    val mediaItem = episode.mediaItem
                    android.widget.Toast.makeText(context, "播放 ${episode.getDisplayTitle()}: ${episode.name}\n文件: ${mediaItem.filePath}", android.widget.Toast.LENGTH_LONG).show()
                    // TODO: 启动播放器播放 mediaItem
                },
                onItemFocused = { episode ->
                    // 可以在这里处理焦点变化，比如更新详情信息
                    android.util.Log.d("VideoDetailsFragment", "Focused on episode ${episode.episodeNumber}: ${episode.name}")
                }
            )

            recyclerView.adapter = episodeAdapter
            recyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(
                context, androidx.recyclerview.widget.LinearLayoutManager.HORIZONTAL, false
            )
        }
    }

    /**
     * 设置季选择下拉框
     */
    private fun setupSeasonSpinner(seasons: List<com.tvplayer.webdav.data.tmdb.TmdbSeason>) {
        spinnerSeason?.let { spinner ->
            val seasonNames = seasons.map { "第${it.seasonNumber}季" }
            val adapter = android.widget.ArrayAdapter(
                requireContext(),
                android.R.layout.simple_spinner_item,
                seasonNames
            )
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            spinner.adapter = adapter

            spinner.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
                override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: View?, position: Int, id: Long) {
                    if (position < seasons.size) {
                        val selectedSeason = seasons[position]
                        viewModel.selectSeason(selectedSeason.seasonNumber)
                    }
                }

                override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
            }
        }
    }

    /**
     * 更新选中的季
     */
    private fun updateSelectedSeason(seasonNumber: Int) {
        // 这里可以更新UI显示当前选中的季
        // 由于spinner的选择会触发viewModel.selectSeason，这里主要用于初始化时设置
    }

    /**
     * 设置剧集列表数据
     */
    private fun setupEpisodes(episodes: List<com.tvplayer.webdav.data.model.Episode>) {
        episodeAdapter?.submitList(episodes)
    }

}
